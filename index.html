<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Christian Prayer Pack - Transform Your Prayer Life</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    
    <style>
        /* Mobile-first dark wholesome design */
        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            min-height: 100vh;
            color: #ecf0f1;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-top: 20px;
        }

        .main-title {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            color: #f8c471;
            margin-bottom: 10px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1rem;
            color: #bdc3c7;
            margin-bottom: 0;
        }

        .form-card {
            background: rgba(52, 73, 94, 0.95);
            border: 1px solid rgba(248, 196, 113, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            padding: 30px 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .form-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #f8c471;
            margin-bottom: 20px;
            text-align: center;
        }

        .form-control {
            background: rgba(236, 240, 241, 0.1);
            border: 1px solid rgba(189, 195, 199, 0.3);
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 1rem;
            width: 100%;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            color: #ecf0f1;
        }

        .form-control::placeholder {
            color: #bdc3c7;
        }

        .form-control:focus {
            outline: none;
            border-color: #f8c471;
            box-shadow: 0 0 0 2px rgba(248, 196, 113, 0.25);
            background: rgba(236, 240, 241, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            border: none;
            border-radius: 8px;
            padding: 15px;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #d35400 0%, #c0392b 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.4);
        }

        .btn-primary:disabled {
            background: #7f8c8d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .privacy-text {
            font-size: 0.8rem;
            color: #bdc3c7;
            text-align: center;
            margin-top: 15px;
        }

        .benefits {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .benefits li {
            padding: 8px 0;
            color: #ecf0f1;
            font-size: 0.9rem;
        }

        .benefits li:before {
            content: "✓";
            color: #f39c12;
            font-weight: bold;
            margin-right: 10px;
        }

        /* Tablet and up */
        @media (min-width: 768px) {
            .container {
                max-width: 500px;
                padding: 40px;
            }

            .main-title {
                font-size: 2.5rem;
            }

            .subtitle {
                font-size: 1.1rem;
            }

            .form-card {
                padding: 40px;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .container {
                max-width: 600px;
            }

            .main-title {
                font-size: 3rem;
            }
        }

        /* Add subtle animation */
        .form-card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="main-title">Transform Your Prayer Life</h1>
            <p class="subtitle">Get your free Christian Prayer Pack and deepen your relationship with God</p>
        </div>

        <!-- Form Card -->
        <div class="form-card">
            <h2 class="form-title">Get Your FREE Prayer Pack</h2>

            <!-- Benefits List -->
            <ul class="benefits">
                <li>30 Powerful Prayer Templates</li>
                <li>Daily Prayer Journal Pages</li>
                <li>Scripture-Based Prayer Guides</li>
                <li>Prayer Request Tracking Sheets</li>
            </ul>

            <!-- Lead Capture Form -->
            <form id="prayerPackForm">
                <input type="text" class="form-control" id="firstName" name="firstName" placeholder="Your First Name" required>

                <input type="email" class="form-control" id="email" name="email" placeholder="Your Email Address" required>

                <button type="submit" class="btn-primary">
                    Send Me My FREE Prayer Pack
                </button>

                <p class="privacy-text">
                    🔒 Your information is safe and will never be shared.
                </p>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Simple form validation and submission
        document.getElementById('prayerPackForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const firstName = document.getElementById('firstName');
            const email = document.getElementById('email');
            const button = document.querySelector('.btn-primary');

            // Basic validation
            if (!firstName.value.trim()) {
                alert('Please enter your first name');
                firstName.focus();
                return;
            }

            if (!email.value.trim() || !email.value.includes('@')) {
                alert('Please enter a valid email address');
                email.focus();
                return;
            }

            // Show loading state
            const originalText = button.textContent;
            button.textContent = 'Sending...';
            button.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                alert(`Thank you, ${firstName.value}! Your Christian Prayer Pack will be sent to ${email.value} shortly. Check your inbox!`);

                // Reset form
                firstName.value = '';
                email.value = '';
                button.textContent = originalText;
                button.disabled = false;
            }, 1500);
        });
    </script>
</body>
</html>
