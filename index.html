<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Christian Prayer Pack - Transform Your Prayer Life</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #DAA520;
            --accent-color: #F5F5DC;
            --text-dark: #2C1810;
        }
        
        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, #F5F5DC 0%, #E6E6FA 100%);
            min-height: 100vh;
        }
        
        .hero-section {
            background: linear-gradient(rgba(139, 69, 19, 0.8), rgba(139, 69, 19, 0.8)), 
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23F5F5DC" width="1200" height="600"/><path fill="%23DAA520" opacity="0.1" d="M0,300 Q300,100 600,300 T1200,300 L1200,600 L0,600 Z"/></svg>');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 80px 0;
        }
        
        .main-title {
            font-family: 'Playfair Display', serif;
            font-size: 3.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
        }
        
        .subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.95;
        }
        
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 40px;
            margin-top: -50px;
            position: relative;
            z-index: 10;
        }
        
        .form-title {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .btn-primary-custom {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
        }
        
        .benefit-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }
        
        .benefit-icon {
            color: var(--secondary-color);
            margin-right: 15px;
            font-size: 1.2rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(218, 165, 32, 0.25);
        }
        
        .privacy-text {
            font-size: 0.85rem;
            color: #666;
            text-align: center;
            margin-top: 1rem;
        }
        
        @media (max-width: 768px) {
            .main-title {
                font-size: 2.5rem;
            }
            .form-container {
                margin: 20px;
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="main-title">Transform Your Prayer Life</h1>
                    <p class="subtitle">
                        <i class="fas fa-praying-hands me-2"></i>
                        Discover the power of focused prayer with our comprehensive Christian Prayer Pack
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Form Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="form-container">
                        <h2 class="form-title">
                            <i class="fas fa-gift me-2"></i>
                            Get Your FREE Prayer Pack
                        </h2>
                        
                        <!-- Benefits List -->
                        <div class="mb-4">
                            <div class="benefit-item">
                                <i class="fas fa-check-circle benefit-icon"></i>
                                <span>30 Powerful Prayer Templates</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-check-circle benefit-icon"></i>
                                <span>Daily Prayer Journal Pages</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-check-circle benefit-icon"></i>
                                <span>Scripture-Based Prayer Guides</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-check-circle benefit-icon"></i>
                                <span>Prayer Request Tracking Sheets</span>
                            </div>
                        </div>

                        <!-- Lead Capture Form -->
                        <form id="prayerPackForm" novalidate>
                            <div class="mb-3">
                                <label for="firstName" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="firstName" name="firstName" required>
                                <div class="invalid-feedback">
                                    Please enter your first name.
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback">
                                    Please enter a valid email address.
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary-custom">
                                    <i class="fas fa-download me-2"></i>
                                    Send Me My FREE Prayer Pack
                                </button>
                            </div>
                            
                            <p class="privacy-text">
                                <i class="fas fa-lock me-1"></i>
                                Your information is safe and will never be shared. Unsubscribe at any time.
                            </p>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation and submission
        document.getElementById('prayerPackForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const form = this;
            const firstName = document.getElementById('firstName');
            const email = document.getElementById('email');
            
            // Reset previous validation states
            form.classList.remove('was-validated');
            
            // Validate form
            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                return;
            }
            
            // Show success message (replace with actual form submission logic)
            const button = form.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
            button.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                alert(`Thank you, ${firstName.value}! Your Christian Prayer Pack will be sent to ${email.value} shortly. Check your inbox!`);
                
                // Reset form
                form.reset();
                form.classList.remove('was-validated');
                
                // Reset button
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        });
    </script>
</body>
</html>
