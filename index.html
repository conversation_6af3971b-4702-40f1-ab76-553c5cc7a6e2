<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Christian Prayer Pack - Transform Your Prayer Life</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    
    <style>
        /* Mobile-first clean design */
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-top: 20px;
        }

        .main-title {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0;
        }

        .form-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px 20px;
            margin-bottom: 20px;
        }

        .form-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .form-control {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px 15px;
            font-size: 1rem;
            width: 100%;
            margin-bottom: 15px;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .btn-primary {
            background-color: #007bff;
            border: none;
            border-radius: 6px;
            padding: 15px;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-primary:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .privacy-text {
            font-size: 0.8rem;
            color: #6c757d;
            text-align: center;
            margin-top: 15px;
        }

        .benefits {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .benefits li {
            padding: 8px 0;
            color: #495057;
            font-size: 0.9rem;
        }

        .benefits li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }

        /* Tablet and up */
        @media (min-width: 768px) {
            .container {
                max-width: 500px;
                padding: 40px;
            }

            .main-title {
                font-size: 2.5rem;
            }

            .subtitle {
                font-size: 1.1rem;
            }

            .form-card {
                padding: 40px;
            }
        }

        /* Desktop */
        @media (min-width: 1024px) {
            .container {
                max-width: 600px;
            }

            .main-title {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="main-title">Transform Your Prayer Life</h1>
            <p class="subtitle">Get your free Christian Prayer Pack and deepen your relationship with God</p>
        </div>

        <!-- Form Card -->
        <div class="form-card">
            <h2 class="form-title">Get Your FREE Prayer Pack</h2>

            <!-- Benefits List -->
            <ul class="benefits">
                <li>30 Powerful Prayer Templates</li>
                <li>Daily Prayer Journal Pages</li>
                <li>Scripture-Based Prayer Guides</li>
                <li>Prayer Request Tracking Sheets</li>
            </ul>

            <!-- Lead Capture Form -->
            <form id="prayerPackForm">
                <input type="text" class="form-control" id="firstName" name="firstName" placeholder="Your First Name" required>

                <input type="email" class="form-control" id="email" name="email" placeholder="Your Email Address" required>

                <button type="submit" class="btn-primary">
                    Send Me My FREE Prayer Pack
                </button>

                <p class="privacy-text">
                    🔒 Your information is safe and will never be shared.
                </p>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Simple form validation and submission
        document.getElementById('prayerPackForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const firstName = document.getElementById('firstName');
            const email = document.getElementById('email');
            const button = document.querySelector('.btn-primary');

            // Basic validation
            if (!firstName.value.trim()) {
                alert('Please enter your first name');
                firstName.focus();
                return;
            }

            if (!email.value.trim() || !email.value.includes('@')) {
                alert('Please enter a valid email address');
                email.focus();
                return;
            }

            // Show loading state
            const originalText = button.textContent;
            button.textContent = 'Sending...';
            button.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                alert(`Thank you, ${firstName.value}! Your Christian Prayer Pack will be sent to ${email.value} shortly. Check your inbox!`);

                // Reset form
                firstName.value = '';
                email.value = '';
                button.textContent = originalText;
                button.disabled = false;
            }, 1500);
        });
    </script>
</body>
</html>
